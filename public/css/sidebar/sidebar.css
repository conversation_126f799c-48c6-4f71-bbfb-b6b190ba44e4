@media (max-width: 576px) {
    .sidebar {
        z-index: 2000;
    }
    .main-content-wrapper::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(30, 41, 59, 0.35);
        z-index: 1500;
        pointer-events: none;
        opacity: 1;
        transition: opacity 0.3s;
    }
    body.sidebar-collapsed .main-content-wrapper::before {
        opacity: 0;
    }
    .d-flex > .sidebar + .main-content-wrapper,
    .d-flex > .sidebar.collapsed + .main-content-wrapper {
        margin-left: 4rem !important;
    }
}

.sidebar {
    width: 17.5rem;
    min-width: 17.5rem;
    max-width: 17.5rem;
    transition: width 0.3s, min-width 0.3s, max-width 0.3s;
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    z-index: 100;
    background-color: var(--darkblue);
    color: white;
    box-sizing: border-box;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.d-flex > .sidebar + .main-content-wrapper {
    margin-left: 17.5rem;
    transition: margin-left 0.3s;
}

.d-flex > .sidebar.collapsed + .main-content-wrapper {
    margin-left: 3.75rem;
}

.main-content-wrapper {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    /* margin-left kaldırıldı */
    transition: margin-left 0.3s;
}

.sidebar.collapsed .sidebar-footer {
    flex-direction: column;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    margin-bottom: 1rem;
    border-bottom: 1px solid #3C4257;
    padding-bottom: 1rem;
    transition: flex-direction 0.3s, gap 0.3s, margin 0.3s;
}

.sidebar-toggle-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    background: none;
    border: none;
    color: white;
    margin-left: 0;
    margin-right: 0;
    transition: margin 0.3s;
}

.sidebar.collapsed .list-unstyled {
    display: flex;
    flex-direction: column;
    align-items: center;
}


.sidebar .nav-link {
    padding: 0.75rem 0;
    color: white;
    display: flex;
    font-weight: 500;
    font-size: 0.8rem !important;
    align-items: center;
    gap: 1.25rem;
    transition: background 0.2s, color 0.2s, padding 0.3s;
}

.sidebar.collapsed {
    width: 3.75rem;
    min-width: 3.75rem;
    max-width: 3.75rem;
    padding: 1.25rem 0.5rem 1.25rem 0.5rem;
}

.sidebar.collapsed .sidebar-header {
    flex-direction: column !important;
    margin-bottom: 1.5rem;
}

.list-unstyled {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.list-unstyled .sidebar-footer {
    border-top: 1px solid hsl(227, 18%, 29%);
    padding-top: 1rem;
}

.sidebar-footer .d-flex.align-items-center {
    gap: 0.25rem;
    transition: flex-direction 0.2s;
}

.sidebar.collapsed .sidebar-footer .d-flex.align-items-center {
    flex-direction: column !important;
    align-items: center !important;
    gap: 0.25rem;
}

.sidebar-footer .profile-name {
    transition: opacity 0.2s, width 0.2s, margin 0.2s;
}
.sidebar.collapsed .sidebar-footer .profile-name {
    opacity: 0;
    width: 0;
    margin: 0;
    pointer-events: none;
    display: none !important;
}




.sidebar.collapsed .sidebar-toggle-btn {
    margin-top: 0.5rem;
    margin-bottom: 0;
}


.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.75rem 0.5rem;
    gap: 0;
}

.sidebar-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 1.25rem;
    min-height: 1.25rem;
    color: white;
}

.sidebar-label {
    transition: opacity 0.2s, width 0.2s, margin 0.2s;
    white-space: nowrap;
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
}

.sidebar.collapsed .sidebar-label {
    opacity: 0;
    width: 0;
    margin: 0;
    pointer-events: none;
    display: none !important;
}
.sidebar.collapsed .sidebar-icon {
    margin-right: 0;
    display: inline-flex !important;
}


.sidebar-profile{
    position: relative;
}

.sidebar-profile img {
    border-radius: 5px;
    object-fit: cover;
}


.sidebar-profile .online-status {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background-color: var(--green); /* Green for online status */
    border: 2px solid white; /* White border for better visibility */
}

.sidebar.collapsed .sidebar-profile {
    margin-bottom: 1rem;
    margin-top: 0.5rem;
}



.sidebar-dropdown .sidebar-dropdown-menu {
    display: none;
    transition: max-height 0.3s cubic-bezier(0.4,0,0.2,1), opacity 0.3s;
    max-height: 0;
    opacity: 0;
    overflow: hidden;
}
.sidebar-dropdown.open .sidebar-dropdown-menu {
    display: block;
    max-height: 500px;
    border-bottom: 1px solid #3C4257;
    padding-bottom: 0.25rem;
    opacity: 1;
    transition: max-height 0.3s cubic-bezier(0.4,0,0.2,1), opacity 0.3s;
}
.sidebar-dropdown .sidebar-dropdown-toggle .iconify:last-child {
    transition: transform 0.3s;
}
.sidebar-dropdown.open .sidebar-dropdown-toggle .iconify:last-child {
    transform: rotate(180deg);
}

.sidebar-dropdown-menu {
    padding-left: 0 !important;
    margin-left: -1.5rem;
    width: calc(100% + 3rem);
    background: transparent;
    position: relative;
}

.sidebar-dropdown-menu .nav-link {
    padding-left: 1.5rem;
    width: 100%;
    display: block;
}

.sidebar-dropdown-menu .nav-link:hover {
    color: #fff;
    background: #3C4257;
}

/* Aktif sidebar label için vurgulu stil */
.sidebar .nav-link.active .sidebar-label {
    font-weight: 700 !important;
}

/* Dropend popup için stil */
.sidebar-dropend-popup {
    position: absolute;
    top: 0;
    margin-left: -0.25rem;
    left: 100%;
    background: var(--darkblue);
    color: white;
    border-radius: 0.5rem;
    width: fit-content;
    z-index: 3000;
    padding: 0.5rem 0.25rem;
    opacity: 1;
    transition: opacity 0.2s;
    border: 1px solid #3C4257;
    display: block;
}
.sidebar-dropend-popup .nav-link {
    padding: 0.5rem 1.25rem;
    color: white;
    font-size: 1rem;
    border-radius: 0.25rem;
    width: 100%;
    display: block;
    background: none;
}
.sidebar-dropend-popup .nav-link:hover {
    background: #3C4257;
    color: #fff;
}

.sidebar.collapsed .sidebar-dropdown.open > .sidebar-dropdown-menu {
    position: absolute;
    left: 100%;
    top: 0;
    min-width: 180px;
    background: var(--darkblue);
    z-index: 3000;
    border-radius: 0.5rem;
    border: 1px solid #3C4257;
    box-shadow: 0 2px 8px rgba(30,41,59,0.08);
    display: block !important;
    opacity: 1;
    margin-left: 0.5rem;
}
.sidebar.collapsed .sidebar-dropdown.open {
    position: relative;
}

  .sidebar-dropdown.open > a .sidebar-icon {
    color: #4B67C2 !important;
    transition: color 0.2s;
  }

  .navbar .admin-nav-links a:hover {
    background: #e8eefc;
    color: #1A1F36 !important;
    transition: background 0.15s, color 0.15s;
    text-decoration: none;
  }
  .navbar .admin-nav-links a:hover .iconify,
  .navbar .admin-nav-links a:hover .nav-link-label
  {
    color: #1A1F36 !important;
    transition: color 0.15s;
  }

  .admin-nav-links.active .nav-link-label,
  .admin-dropdown.active .iconify,
  .admin-nav-links .flex-fill.active .iconify,
  .admin-nav-links .flex-fill.active .nav-link-label
  {
    color: #1A1F36 !important;
  }

.floating-dialpad-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 56px;
    height: 56px;
    border-radius: 100px;
    background: #30CB83;
    border: none;
    color: white;
    box-shadow: 0 0 15px 0 #00000026;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1040;
}

.floating-dialpad-btn:hover {
    background: #2BB876;
}

.websocket-status-item {
    margin-top: auto;
    border-top: 1px solid #3C4257;
    padding-top: 0.75rem;
    position: sticky;
    bottom: 0;
    background-color: var(--darkblue);
    position: relative;
}

.websocket-status-indicator {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background-color: #E74C3C; /* Kırmızı - bağlantısız */
    margin-right: 1rem;
    transition: background-color 0.3s;
}

.websocket-status-indicator.connected {
    background-color: #30CB83; /* Yeşil - bağlı */
}

.websocket-label {
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
    transition: opacity 0.2s, width 0.2s, margin 0.2s;
}

.websocket-ping-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s;
    z-index: 1000;
}

.websocket-status-item:hover .websocket-ping-tooltip {
    opacity: 1;
    visibility: visible;
}

.sidebar.collapsed .websocket-label {
    opacity: 0;
    width: 0;
    margin: 0;
    pointer-events: none;
    display: none !important;
}

.sidebar.collapsed .websocket-status-item .nav-link {
    justify-content: center;
}

.sidebar.collapsed .websocket-status-indicator {
    margin-right: 0;
}
