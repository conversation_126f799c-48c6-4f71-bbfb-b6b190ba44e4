<?php

use App\Http\Controllers\AdminController;
use App\Http\Controllers\AgentController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CallController;
use App\Http\Controllers\HomeController;
use Illuminate\Support\Facades\Route;


// Giriş sayfası (tek route)
Route::get('/giris', [AuthController::class, 'login'])->name('login');

// Giriş formunun post işlemi
Route::post('/login', [AuthController::class, 'attempt'])->name('login.attempt');

// Çıkış işlemi
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
Route::get('/logout', [AuthController::class, 'logout']);

// Sadece giriş yapmış kullanıcılar için ana sayfa
Route::middleware(['auth'])->group(function () {

    // Sidebar
    Route::get('/', [HomeController::class, 'index'])->name('home');

    // AdminController'a ait tüm rotalar
    Route::prefix('admin')->group(function () {
        Route::get('/santral-izleme', [AdminController::class, 'monitoring'])->name('monitoring');
        Route::get('/dahili-listesi', [AdminController::class, 'internalList'])->name('internal-list');
        Route::get('/dahili-gruplari', [AdminController::class, 'internalGroups'])->name('internal-groups');
        Route::get('/grup-kullanicilari', [AdminController::class, 'groupUsers'])->name('group-users');
        Route::get('/kuyruk-listesi', [AdminController::class, 'queueList'])->name('queue-list');
        Route::get('/bekleme-muzikleri', [AdminController::class, 'waitingMusic'])->name('waiting-music');
        Route::get('/anons-ses-kayitlari', [AdminController::class, 'anonsRecording'])->name('anons-recording');
        Route::get('/ivr-listesi', [AdminController::class, 'ivrList'])->name('ivr-list');
        Route::get('/dinamik-ivr-listesi', [AdminController::class, 'dynamicIvrList'])->name('dynamic-ivr-list');
        Route::get('/puanlama-listesi', [AdminController::class, 'scoreList'])->name('score-list');
        Route::get('/ivr-ses-kayitlari', [AdminController::class, 'ivrRecording'])->name('ivr-recording');
        Route::get('/dis-hat-islemleri', [AdminController::class, 'externalLines'])->name('external-lines');
        Route::get('/mesai-sablonlari', [AdminController::class, 'shiftTemplates'])->name('shift-templates');
        Route::get('/arama-yetkilendirme', [AdminController::class, 'callAuthorization'])->name('call-authorization');
        Route::get('/karaliste', [AdminController::class, 'blacklist'])->name('blacklist');
        Route::get('/cagrilar', [AdminController::class, 'calls'])->name('calls');
        Route::get('/cagri-durumu', [AdminController::class, 'callStatus'])->name('call-status');
        Route::get('/dahili-durumu', [AdminController::class, 'internalStatus'])->name('internal-status');
        Route::get('/hat-durumu', [AdminController::class, 'lineStatus'])->name('line-status');
        Route::get('/gelismis-ozellikler', [AdminController::class, 'advancedFeatures'])->name('advanced-features');
        Route::get('/iletisim', [AdminController::class, 'contact'])->name('contact');
        Route::get('/cc-dashboard', [AdminController::class, 'ccDashboard'])->name('cc-dashboard');
        Route::get('/musteri-listesi', [AdminController::class, 'customerList'])->name('customer-list');
        Route::get('/musteri-gruplari', [AdminController::class, 'customerGroups'])->name('customer-groups');
        Route::get('/musteri-kriterleri', [AdminController::class, 'customerCriteria'])->name('customer-criteria');
        Route::get('/musteri-tipleri', [AdminController::class, 'customerTypes'])->name('customer-types');
        Route::get('/planlanmis-cagrilar', [AdminController::class, 'plannedCalls'])->name('planned-calls');
        Route::get('/cagri-sonuc-tipleri', [AdminController::class, 'callResultTypes'])->name('call-result-types');
        Route::get('/cevapsiz-cagrilar', [AdminController::class, 'missedCalls'])->name('missed-calls');
        Route::get('/cevapsiz-gelen-cagri', [AdminController::class, 'missedIncomingCalls'])->name('missed-incoming-calls');
        Route::get('/geri-aranma-talepleri', [AdminController::class, 'callbackRequests'])->name('callback-requests');
        Route::get('/otomatik-arama', [AdminController::class, 'autoCall'])->name('auto-call');
        Route::get('/randevu-teyit', [AdminController::class, 'meetConfirmation'])->name('meet-confirmation');
        Route::get('/oto-anket', [AdminController::class, 'autoSurvey'])->name('auto-survey');
        Route::get('/data-listesi', [AdminController::class, 'dataList'])->name('data-list');
        Route::get('/data-gruplari', [AdminController::class, 'dataGroups'])->name('data-groups');
        Route::get('/yetkilendirmeler', [AdminController::class, 'authorizations'])->name('authorizations');
        Route::get('/agent-mola-tipleri', [AdminController::class, 'agentBreakTypes'])->name('agent-break-types');
        Route::get('/calisma-istatistikleri', [AdminController::class, 'workStatistics'])->name('work-statistics');
        Route::get('/agent-durumlari', [AdminController::class, 'agentStatus'])->name('agent-status');
        Route::get('/agent-loglari', [AdminController::class, 'agentLogs'])->name('agent-logs');
    });

    // HomeController
    Route::get('/profilim', [HomeController::class, 'profile'])->name('profile');

    // AgentController
    Route::get('/cagrilar', [CallController::class, 'calls'])->name('calls');
    Route::get('/planlanmis-cagrilar', [AgentController::class, 'plannedCalls'])->name('planned-calls');
    Route::get('/agent-durumlari', [AgentController::class, 'agentStatus'])->name('agent-status');
    Route::get('/musteri-listesi', [AgentController::class, 'customerList'])->name('customer-list');
});
