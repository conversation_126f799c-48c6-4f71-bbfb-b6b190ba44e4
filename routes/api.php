<?php

use App\Http\Controllers\CallController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\CustomerGroupController;
use App\Http\Controllers\CustomerCriteriaController;
use App\Http\Controllers\CustomerTypeController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

Route::apiResource('customers', CustomerController::class);
Route::apiResource('customer-groups', CustomerGroupController::class);
Route::apiResource('customer-criteria', CustomerCriteriaController::class);
Route::apiResource('customer-types', CustomerTypeController::class);
Route::apiResource('calls', CallController::class);
Route::post('/call-recording', [CallController::class, 'storeRecording']);
Route::get('users', [UserController::class, 'index']);
Route::get('users/agents', [UserController::class, 'getAgents']);

// Bulk delete routes
Route::delete('customers', [CustomerController::class, 'destroyMultiple']);
Route::delete('customer-groups', [CustomerGroupController::class, 'destroyMultiple']);
Route::delete('customer-criteria', [CustomerCriteriaController::class, 'destroyMultiple']);
Route::delete('customer-types', [CustomerTypeController::class, 'destroyMultiple']);
 