<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCallRequest;
use App\Http\Requests\UpdateCallRequest;
use App\Services\CallService;
use App\Traits\CrudActions;
use Illuminate\Http\Request;

class CallController extends Controller
{
    use CrudActions;

    public function __construct(CallService $service)
    {
        $this->service = $service;
    }

    protected function getStoreRequestClass(): string
    {
        return StoreCallRequest::class;
    }

    protected function getUpdateRequestClass(): string
    {
        return UpdateCallRequest::class;
    }

    protected function getResourceName(): string
    {
        return 'çağrı';
    }
    public function calls() {
        $calls = $this->service->getAllCategorized();
        if (request()->ajax()) {
            return view('user.calls.content', compact('calls'))->render();
        }
        return view('user.calls.index', compact('calls'));
    }

    public function storeRecording(Request $request)
    {
        $file = $request->file('file');
        $call_id = $request->input('call_id');

        if (!$file || !$call_id) {
            return response()->json(['error' => 'File and call ID are required'], 400);
        }

        $this->service->storeRecording([
            'file' => $file,
            'call_id' => $call_id,
        ]);

        return response()->json(['message' => 'Call recording stored successfully'], 201);
    
    }
}
