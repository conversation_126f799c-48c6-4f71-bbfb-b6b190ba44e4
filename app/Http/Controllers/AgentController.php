<?php

namespace App\Http\Controllers;

class AgentController extends Controller
{

    public function plannedCalls() {
        if (request()->ajax()) {
            return view('user.planned-calls.content')->render();
        }
        return view('user.planned-calls.index');
    }

    public function agentStatus() {
        $agents = \App\Models\User::where('role', 'agent')->where('company_id', auth()->user()->company_id)->get();
        if (request()->ajax()) {
            return view('user.agent-status.content', compact('agents'))->render();
        }
        return view('user.agent-status.index', compact('agents'));
    }

    public function customerList() {
        if (request()->ajax()) {
            return view('user.customer-list.content')->render();
        }
        return view('user.customer-list.index');
    }
}
