<?php

namespace App\Http\Controllers;

use App\Models\Call;

class HomeController extends Controller
{
    public function index()
    {
        $view_tag = request()->ajax() ? 'content' : 'index';
        if (auth()->user()->role === 'admin') {
            return view('admin.home.' . $view_tag);
        } else {
            $incoming_calls_count = Call::where('user_id', auth()->id())->count();
            $outgoing_calls_count = Call::where('user_id', auth()->id())->where('type', 'OUTGOING')->count();
            $data = [
                'user' => auth()->user(),
                'incoming_calls_count' => $incoming_calls_count,
                'outgoing_calls_count' => $outgoing_calls_count,
            ];
            return view('user.home.' . $view_tag, compact('data'));
        }
    }

    public function profile()
    {
        return view('admin.profile.index');
    }
}
