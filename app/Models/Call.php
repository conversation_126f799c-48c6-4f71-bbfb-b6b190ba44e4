<?php

namespace App\Models;

use App\Enum\CallStatus;
use App\Enum\CallType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Call extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'caller_id',
        'hash',
        'duration',
        'status',
        'type',
        'record_path',
        'dial_number',
        'company_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    // İlişkiler
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
    public function getStatusAttribute()
    {
        return CallStatus::tryFrom($this->attributes['status']) ?? CallStatus::COMPLETED;
    }

    public function getStatusLabelAttribute()
    {
        return match ($this->status) {
            CallStatus::DIALING   => 'Aranıyor',
            CallStatus::MISSED    => 'Cevapsız',
            CallStatus::ON_CALL   => 'Aranıyor',
            CallStatus::FAILED    => 'Başarısız',
            CallStatus::COMPLETED => 'Tamamlandı',
            default               => 'Bilinmiyor',
        };
    }

    public function getTypeAttribute()
    {
        return CallType::tryFrom($this->attributes['type']) ?? CallType::INCOMING;
    }

    public function getTypeLabelAttribute()
    {
        return match ($this->type) {
            CallType::OUTGOING => 'Giden',
            CallType::INCOMING => 'Gelen',
            default    => 'Bilinmiyor',
        };
    }
}
