<style>
.statisctic-card {
    border: 1px solid #F3F4F6;
    border-radius: 0.75rem;
    background-color: #FCFCFB;
    padding: 1.25rem;
    display: flex;
    flex-direction: column;
    row-gap: 1rem;
    justify-content: space-between;
    height: 100%;
}

.statisctic-card .icon-circle {
    height: 48px;
    width: 48px;
    border-radius: 50%;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #F3F4F6;
    padding: 4px;
}

.statisctic-card .iconify {
    color: #4B5563;
    font-size: 1.25rem;
}

.statisctic-card h5,
.statisctic-card span {
    font-weight: 600;
    color: var(--black);
    margin-bottom: 0;
}

.statisctic-card h5 {
    font-size: 1rem;
    font-weight: 600;
}

.statisctic-card span {
    font-size: 1rem;
    font-weight: 400;
}

</style>

<div class="statisctic-card">
    <div class="icon-circle">
        <span class="iconify" data-icon="{{ icon }}" data-inline="false" style="color: {{ color }};"></span>
    </div>
    <h5 class=" mb-auto">{{ title }}</h5>
    <span class="">{{ value }}</span>
</div>