<table id="{{ id|default('table') }}" class="display custom-data-table" style="width:100%; ">
  <thead>
    <tr>
      {% for header in headers %}
        {% if header is iterable and header.type == 'checkbox' %}
          <th class="{{ header.class|default('') }}">
            <label class="custom-checkbox">
              <input type="checkbox" id="{{ header.name|default('checkAll') }}" />
              <span class="custom-check">
                <span class="iconify" data-icon="material-symbols:check-small-rounded" data-inline="false"></span>
              </span>
            </label>
          </th>
        {% else %}
          <th>{{ header }}</th>
        {% endif %}
      {% endfor %}
    </tr>
  </thead>
  <tbody>
    {% for row in rows %}
      <tr>
        {% for cell in row %}
          {% if cell is iterable and cell.type == 'checkbox' %}
            <td>
              <label class="custom-checkbox">
                <input type="checkbox" class="row-check" />
                <span class="custom-check">
                  <span class="iconify" data-icon="material-symbols:check-small-rounded" data-inline="false"></span>
                </span>
              </label>
            </td>
          {% elseif cell is iterable and cell.type == 'html' %}
            <td>{{ cell.html|raw }}</td>
          {% else %}
            <td>{{ cell }}</td>
          {% endif %}
        {% endfor %}
      </tr>
    {% endfor %}
  </tbody>
  {% if footers is defined %}
  <tfoot>
    <tr>
      {% for footer in footers %}
        <th class="custom-table-footer-cell">{{ footer }}</th>
      {% endfor %}
    </tr>
  </tfoot>
  {% endif %}
</table>