
<style>
.dialpad-panel {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 20rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    user-select: none;
    transform-origin: bottom right;
}

.dialpad-panel.show {
    animation: scaleIn 0.2s ease-out forwards;
}

.dialpad-panel.hide {
    animation: scaleOut 0.2s ease-out forwards;
}

.dialpad-header h5{
    font-size: 1rem;
    font-weight: 500;
    color: #444444;
}

.dialpad-grid {
    display: grid;
    grid-template-columns: repeat(3, 70px);
    gap: 16px;
    justify-content: center;
    margin: 0 auto;
}

.dialpad-button {
    width: 54px;
    height: 54px;
    border-radius: 50%;
    background: #FCFCFB;
    border: 1px solid #F3F4F6;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
}


.dialpad-button .number {
    color: #444444;
    font-size: 1.25rem;
    font-weight: 600;
}

.dialpad-button .letters {
    font-size: 12px;
    font-weight: 400;
    color: #94A3B8;
}

.call-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #30CB83;
    border: 1px solid #34D399;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-inline: auto;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.call-btn .iconify {
    color: white;
    font-size: 1.5rem;
}


.dial-bottom{
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
}

.dial-bottom-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.dial-bottom-item .iconify {
    font-size: 1.5rem;
    color: #94A3B8;
    margin-bottom: 0.25rem;
}

.dial-bottom-item .dial-bottom-text {
    font-size: 12px;
    color: #94A3B8;
}

.dial-bottom-item.active .iconify,
.dial-bottom-item.active .dial-bottom-text
{
    color: #4B67C2;
}

#dialedNumber {
    font-size: 1.5rem;
    color: #444444;
    font-weight: 500;
}


.day-text{
    font-size: 14px;
    font-weight: 600;
    color: #444444;
    margin-bottom: 1rem;
}


.call-info{
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #E5E7EB;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.call-name{
    color: #444444;
    font-size: 14px;
    font-weight: 500;
}

.call-time{
    color: #94A3B8;
    font-size: 14px;
    font-weight: 500;
}

.call-info .iconify[data-icon="hugeicons:call-incoming-04"]{
    color: #E74C3C;
    font-size: 18px;
}

.call-info .iconify[data-icon="hugeicons:call-outgoing-04"]{
    color: #16A34A;
    font-size: 18px;
}

.call-again-btn {
    background-color: #30CB83;
    color: white;
    border-radius: 50%;
    margin-right: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    cursor: pointer;
    padding: 0.5rem;
}

.delete-call-btn {
    background-color: #E74C3C;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    padding: 0.5rem;
}

.call-profile-card{
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    background-color: #FCFCFB;
    border: 1px solid #F3F4F6;
    border-radius: 12px;
    gap: 1rem;
    flex-direction: column;
    margin-block: 1rem;
}

.call-profile-avatar{
    height: 85px;
    width: 85px;
    border-radius: 50%;
    border: 1px solid #F3F4F6;
    background-color: #E5E7EB;
    display: flex;
    align-items: center;
    justify-content: center;
}

.call-profile-avatar .iconify {
    font-size: 2rem;
    color: #94A3B8;
}

.call-profile-card h6 {
    color: #444444;
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 0.25rem;
}

.call-profile-card span {
    color: #444444;
    font-size: 16px;
    font-weight: 500;
}

.mute-btn{
    border: 1px solid #F3F4F6;
    background-color: #FFFFFF;
    font-size: 50px;
    padding: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #444444;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.mute-btn:hover {
    background-color: #F3F4F6;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.mute-btn:active {
    transform: scale(0.95);
}

.mute-btn.active {
    background-color: var(--black);
    border-color: var(--black);
    color: white;
}

.speaker-btn{
    border: 1px solid #4B67C2;
    background-color: #4B67C2;
    font-size: 50px;
    padding: 0.75rem;
    display: flex;
    align-items: center;
    color: white;
    border-radius: 50%;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.speaker-btn:hover {
    background-color: #3B5198;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(75, 103, 194, 0.3);
}

.speaker-btn:active {
    transform: scale(0.95);
}

.speaker-btn.active {
    animation: pulse 1.5s infinite;
}

.end-call{
    border: 1px solid #E74C3C;
    background-color: #E74C3C;
    font-size: 50px;
    padding: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.end-call:hover {
    background-color: #C0392B;
    transform: scale(1.05) rotate(12deg);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.end-call:active {
    transform: scale(0.95) rotate(0deg);
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(75, 103, 194, 0.4);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(75, 103, 194, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(75, 103, 194, 0);
    }
}

@keyframes slideInUp {
    from {
        transform: translate3d(0, 30px, 0);
        opacity: 0;
    }
    to {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@keyframes slideOutDown {
    from {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
    to {
        transform: translate3d(0, 30px, 0);
        opacity: 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0.95);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes scaleOut {
    from {
        transform: scale(1);
        opacity: 1;
    }
    to {
        transform: scale(0.95);
        opacity: 0;
    }
}

/* Geçiş animasyonları için class'lar */
.animate-slide-in {
    animation: slideInUp 0.15s ease-out forwards;
}

.animate-slide-out {
    animation: slideOutDown 0.15s ease-out forwards;
}

.animate-fade-in {
    animation: fadeIn 0.15s ease-out forwards;
}

.animate-fade-out {
    animation: fadeOut 0.15s ease-out forwards;
}

/* Dial bottom için smooth transition */
.dial-bottom {
    transition: all 0.15s ease-out;
}

/* Content geçişleri için base style */
#activeCallContent,
#dialpadContent,
#recentCallsContent,
#contactsContent {
    transition: all 0.15s ease-out;
}

/* Contacts Styles */
.contact-search-box {
    position: relative;
}

.contact-search-box .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
    color: #94A3B8;
}

.contact-search {
    padding-left: 40px !important;
    background-color: #F8FAFC;
    border: 1px solid #F1F5F9;
    height: 42px;
}

.contact-search:focus {
    background-color: #FFFFFF;
    border-color: #4B67C2;
    box-shadow: none;
}

.contacts-count {
    font-size: 12px;
    color: #94A3B8;
    font-weight: 500;
}

.contacts-list {
    max-height: 400px;
    overflow-y: auto;
    margin-right: -1rem;
    padding-right: 1rem;
}

.contacts-list::-webkit-scrollbar, #recentCallsContent > div::-webkit-scrollbar {
    width: 4px;
}

.contacts-list::-webkit-scrollbar-track, #recentCallsContent > div::-webkit-scrollbar-track {
    background: #F1F5F9;
    border-radius: 2px;
}

.contacts-list::-webkit-scrollbar-thumb, #recentCallsContent > div::-webkit-scrollbar-thumb {
    background: #CBD5E1;
    border-radius: 2px;
}

.contact-group {
    margin-bottom: 1.5rem;
}

.contact-letter {
    font-size: 14px;
    font-weight: 600;
    color: #444444;
    margin-bottom: 0.75rem;
}

.contact-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.contact-item {
    padding: 0.75rem;
    background: #FCFCFB;
    border: 1px solid #F3F4F6;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.contact-item:hover {
    background: #F8FAFC;
    transform: translateX(4px);
}

.contact-name {
    font-size: 14px;
    font-weight: 500;
    color: #444444;
    margin-bottom: 0.25rem;
}

.contact-number {
    font-size: 12px;
    color: #94A3B8;
}

/* SIP Status Indicator */
.sip-status {
    display: flex;
    align-items: center;
    font-size: 11px;
    color: #94A3B8;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #FFA500;
    margin-right: 4px;
    animation: pulse-orange 2s infinite;
}

.sip-status.connected .status-dot {
    background-color: #30CB83;
    animation: none;
}

.sip-status.disconnected .status-dot {
    background-color: #E74C3C;
    animation: none;
}

.sip-status.error .status-dot {
    background-color: #E74C3C;
    animation: pulse-red 1s infinite;
}

@keyframes pulse-orange {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 165, 0, 0.7);
    }
    70% {
        box-shadow: 0 0 0 4px rgba(255, 165, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 165, 0, 0);
    }
}

@keyframes pulse-red {
    0% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
    }
    70% {
        box-shadow: 0 0 0 4px rgba(231, 76, 60, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
    }
}

</style>




<!-- Dialpad Panel -->
<div id="dialpadPanel" class="dialpad-panel" style="display: none;">
    <!-- Header -->
    <div class="dialpad-header d-flex justify-content-between align-items-center p-3">
        <div class="d-flex align-items-center">
            <h5 class="m-0 me-2">Çağrı Yap</h5>
            <div id="sipStatus" class="sip-status" title="SIP Bağlantı Durumu">
                <span class="status-dot"></span>
                <span class="status-text">Bağlanıyor...</span>
            </div>
        </div>
        <span style="font-size: 20px; cursor: pointer;" class="iconify close-btn" data-icon="ic:round-horizontal-rule" id="closeDialpad" data-inline="false"></span>
    </div>
    <hr class="custom-hr m-0">

    <div class="dialpad-body">
        <div class="d-flex flex-column">
            <!-- Son Aramalar İçeriği -->
            <div id="recentCallsContent" class="d-none">
                <div class="d-flex flex-column p-4" style="max-height: 400px; overflow-y: auto;">
                    <span class="day-text">Bugün</span>
                    <div class="call-info">
                        <div class="d-flex align-items-center">
                            <span class="iconify" data-icon="hugeicons:call-incoming-04" data-inline="false"></span>
                            <div class="d-flex flex-column ms-3">
                                <h6 class="call-name mb-0">Hardik Pandya</h6>
                                <span class="call-time d-flex align-items-center">
                                    <span class="iconify text-dark" data-icon="solar:clock-circle-linear" data-inline="false"></span>
                                    <b class="mx-1 text-dark">10:30</b>
                                    00:56
                                </span>
                            </div>
                        </div>
                         <div class="d-flex align-items-center">
                            <span class="iconify call-again-btn" data-icon="fluent:call-outbound-16-filled" data-inline="false"></span>
                            <span class="iconify delete-call-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                        </div>
                    </div>
                    <div class="call-info">
                        <div class="d-flex align-items-center">
                           <span class="iconify" data-icon="hugeicons:call-outgoing-04" data-inline="false"></span>
                            <div class="d-flex flex-column ms-3">
                                <h6 class="call-name mb-0">Hardik Pandya</h6>
                                <span class="call-time d-flex align-items-center">
                                    <span class="iconify text-dark" data-icon="solar:clock-circle-linear" data-inline="false"></span>
                                    <b class="mx-1 text-dark">10:30</b>
                                    00:56
                                </span>
                            </div>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="iconify call-again-btn" data-icon="fluent:call-outbound-16-filled" data-inline="false"></span>
                            <span class="iconify delete-call-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                        </div>
                    </div>
                    <!-- <div class="text-center">Son aramalarınız burada görüntülenecek</div> -->
                </div>
            </div>

            <!-- Tuş Takımı İçeriği -->
            <div id="dialpadContent">
                <div class="d-flex align-items-center mt-4 px-4">
                    <input type="text" id="dialedNumber" class="form-control p-2 text-center" style="border: none !important;" readonly>
                    <button type="button" class="btn btn-link p-0 text-dark" id="clearNumber">
                        <span class="iconify" data-icon="ph:backspace-light" style="font-size: 20px; margin-bottom: 0.15rem;" data-inline="false"></span>
                    </button>
                </div>

                <div class="dialpad-grid p-4">
                    <div class="dialpad-button" data-value="1">
                        <div class="number">1</div>
                        <div class="letters">.</div>
                    </div>
                    <div class="dialpad-button" data-value="2">
                        <div class="number">2</div>
                        <div class="letters">ABC</div>
                    </div>
                    <div class="dialpad-button" data-value="3">
                        <div class="number">3</div>
                        <div class="letters">DEF</div>
                    </div>
                    <div class="dialpad-button" data-value="4">
                        <div class="number">4</div>
                        <div class="letters">GHI</div>
                    </div>
                    <div class="dialpad-button" data-value="5">
                        <div class="number">5</div>
                        <div class="letters">JKL</div>
                    </div>
                    <div class="dialpad-button" data-value="6">
                        <div class="number">6</div>
                        <div class="letters">MNO</div>
                    </div>
                    <div class="dialpad-button" data-value="7">
                        <div class="number">7</div>
                        <div class="letters">PQRS</div>
                    </div>
                    <div class="dialpad-button" data-value="8">
                        <div class="number">8</div>
                        <div class="letters">TUV</div>
                    </div>
                    <div class="dialpad-button" data-value="9">
                        <div class="number">9</div>
                        <div class="letters">WXYZ</div>
                    </div>
                    <div class="dialpad-button" data-value="*">
                        <div class="number">*</div>
                    </div>
                    <div class="dialpad-button" data-value="0">
                        <div class="number">0</div>
                        <div class="letters">+</div>
                    </div>
                    <div class="dialpad-button" data-value="#">
                        <div class="number">#</div>
                    </div>
                </div>

                <div class="call-btn mb-4">
                    <span class="iconify" data-icon="mingcute:phone-fill" data-inline="false"></span>
                </div>
            </div>

            <!-- Gelen Çağrı İçeriği -->
            <div id="incomingCallContent" class="d-none">
                <div class="d-flex flex-column p-4">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <span class="iconify" style="font-size: 16px; color: #10B981;" data-icon="hugeicons:call-incoming-04" data-inline="false"></span>
                        <span class="ms-2" style="font-size: 14px; color: #10B981; font-weight: 500;">Gelen Arama</span>
                    </div>
                    <div class="call-profile-card">
                        <div class="call-profile-avatar">
                            <span class="iconify" data-icon="mage:user-fill" data-inline="false"></span>
                        </div>
                        <div class="d-flex flex-column align-items-center">
                            <h6 id="incomingCallName">Gelen Arama</h6>
                            <span id="incomingCallNumber">-</span>
                        </div>
                    </div>
                    <div class="d-flex justify-content-center gap-4 mt-3">
                        <button id="rejectCallBtn" class="btn btn-danger d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; border-radius: 50%; border: none;">
                            <span class="iconify" style="font-size: 24px;" data-icon="solar:end-call-bold" data-inline="false"></span>
                        </button>
                        <button id="acceptCallBtn" class="btn btn-success d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; border-radius: 50%; border: none;">
                            <span class="iconify" style="font-size: 24px;" data-icon="mingcute:phone-fill" data-inline="false"></span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Aktif Çağrı İçeriği -->
            <div id="activeCallContent" class="d-none">
                <div class="d-flex flex-column p-4">
                    <div class="d-flex align-items-center justify-content-center">
                        <span class="iconify" style="font-size: 16px; color: #94A3B8;" data-icon="hugeicons:call-outgoing-04" data-inline="false"></span>
                        <span id="callTimer" class="ms-1" style="font-size: 12px; color: #94A3B8; font-weight: 500;">00:00</span>
                    </div>
                    <div class="call-profile-card">
                        <div class="call-profile-avatar">
                            <span class="iconify" data-icon="mage:user-fill" data-inline="false"></span>
                        </div>
                        <div class="d-flex flex-column align-items-center">
                            <h6 id="activeCallName">-</h6>
                            <span id="activeCallNumber">-</span>
                        </div>
                    </div>
                    <div class="d-flex justify-content-center gap-3">
                        <span class="iconify mute-btn" data-icon="fluent:mic-off-32-regular" data-inline="false"></span>
                        <span class="iconify end-call" data-icon="solar:end-call-bold" data-inline="false"></span>
                        <span class="iconify speaker-btn" data-icon="ph:speaker-high-light" data-inline="false"></span>
                    </div>
                </div>
            </div>

            <!-- Kişiler İçeriği -->
            <div id="contactsContent" class="d-none">
                <div class="d-flex flex-column p-4">
                    <!-- Arama Alanı -->
                    <div class="contact-search-box mb-3">
                        <span class="iconify search-icon" data-icon="hugeicons:search-01" data-inline="false"></span>
                        <input type="text" class="form-control contact-search" placeholder="Ara...">
                    </div>

                    <!-- Toplam Kişi Sayısı -->
                    <div class="contacts-count mb-3">
                        1930 kişi sisteme kayıtlı
                    </div>

                    <!-- Kişiler Listesi -->
                    <div class="contacts-list">
                        <!-- A Grubu -->
                        <div class="contact-group">
                            <div class="contact-letter">A</div>
                            <div class="contact-items">
                                <div class="contact-item d-flex justify-content-between align-items-center">
                                    <div class="d-flex flex-column">
                                        <div class="contact-name">ABC</div>
                                        <div class="contact-number">+917622365663</div>
                                    </div>
                                    <span class="iconify call-again-btn ms-auto" data-icon="fluent:call-outbound-16-filled" data-inline="false"></span>
                                </div>
                                <div class="contact-item d-flex justify-content-between align-items-center">
                                    <div class="d-flex flex-column">
                                        <div class="contact-name">ABCD</div>
                                        <div class="contact-number">+917622365663</div>
                                    </div>
                                    <span class="iconify call-again-btn ms-auto" data-icon="fluent:call-outbound-16-filled" data-inline="false"></span>
                                </div>
                            </div>
                        </div>

                        <!-- B Grubu -->
                        <div class="contact-group">
                            <div class="contact-letter">B</div>
                            <div class="contact-items">
                                <div class="contact-item d-flex justify-content-between align-items-center">
                                    <div class="d-flex flex-column">
                                        <div class="contact-name">BCD</div>
                                        <div class="contact-number">+917622365663</div>
                                    </div>
                                    <span class="iconify call-again-btn ms-auto" data-icon="fluent:call-outbound-16-filled" data-inline="false"></span>
                                </div>
                                <div class="contact-item d-flex justify-content-between align-items-center">
                                    <div class="d-flex flex-column">
                                        <div class="contact-name">BCDaa</div>
                                        <div class="contact-number">+917622365663</div>
                                    </div>
                                    <span class="iconify call-again-btn ms-auto" data-icon="fluent:call-outbound-16-filled" data-inline="false"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <hr class="custom-hr m-0">

            <div class="dial-bottom p-4">
                <div class="dial-bottom-item" data-target="recentCalls">
                    <span class="iconify" data-icon="solar:clock-circle-bold" data-inline="false"></span>
                    <span class="dial-bottom-text">Son Aramalar</span>
                </div>
                <div class="dial-bottom-item active" data-target="dialpad">
                    <span class="iconify" data-icon="ic:baseline-dialpad" data-inline="false"></span>
                    <span class="dial-bottom-text">Ara</span>
                </div>
                <div class="dial-bottom-item" data-target="contacts">
                    <span class="iconify" data-icon="solar:users-group-two-rounded-bold" data-inline="false"></span>
                    <span class="dial-bottom-text">Kişiler</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const dialedNumber = document.getElementById('dialedNumber');
    const clearNumber = document.getElementById('clearNumber');
    const dialpadButtons = document.querySelectorAll('.dialpad-button');
    const closeDialpad = document.getElementById('closeDialpad');
    const dialpadPanel = document.getElementById('dialpadPanel');
    const bottomItems = document.querySelectorAll('.dial-bottom-item');
    const callBtn = document.querySelector('.call-btn');
    const activeCallContent = document.getElementById('activeCallContent');
    const incomingCallContent = document.getElementById('incomingCallContent');
    const dialBottom = document.querySelector('.dial-bottom');

    let sipManager = null;
    let isInitializingSIP = false;

    // Call timer variables
    let callTimer = null;
    let callStartTime = null;
    let callDuration = 0;

    // Çağrı kontrol butonları
    const muteBtn = document.querySelector('.mute-btn');
    const speakerBtn = document.querySelector('.speaker-btn');
    const endCallBtn = document.querySelector('.end-call');

    // Gelen çağrı kontrol butonları
    const acceptCallBtn = document.getElementById('acceptCallBtn');
    const rejectCallBtn = document.getElementById('rejectCallBtn');

    // Aktif çağrı bilgileri
    const activeCallName = document.getElementById('activeCallName');
    const activeCallNumber = document.getElementById('activeCallNumber');

    // Gelen çağrı bilgileri
    const incomingCallName = document.getElementById('incomingCallName');
    const incomingCallNumber = document.getElementById('incomingCallNumber');

    // Gelen çağrı durumu
    let currentIncomingInvitation = null;

    // Ringtone audio element
    let ringtoneAudio = null;

    // Tab içerikleri
    const contents = {
        recentCalls: document.getElementById('recentCallsContent'),
        dialpad: document.getElementById('dialpadContent'),
        contacts: document.getElementById('contactsContent'),
        incomingCall: document.getElementById('incomingCallContent'),
        activeCall: document.getElementById('activeCallContent')
    };

    // Initialize SIP Manager
    async function initializeSIP() {
        if (isInitializingSIP || sipManager) {
            return;
        }

        isInitializingSIP = true;
        updateSIPStatus('connecting');

        try {
            sipManager = new SIPManager();

            // Initialize ringtone
            initializeRingtone();

            // Enable audio context on first user interaction
            enableAudioOnUserInteraction();

            // Set up event callbacks
            sipManager.onCallStarted = function(phoneNumber) {
                console.log('Call started to:', phoneNumber);
                updateCallUI(phoneNumber, 'connecting');
                // Note: Timer will start when call is established, not just started
            };

            sipManager.onCallEstablished = function(phoneNumber) {
                console.log('onCallEstablished callback triggered for:', phoneNumber);
                console.log('Timer currently active:', !!callTimer);
                updateCallUI(phoneNumber, 'connected');
                startCallTimer();
            };

            sipManager.onCallEnded = function() {
                console.log('SIP call ended callback triggered');
                console.log('Timer active before cleanup:', !!callTimer);
                console.log('Active call UI visible:', !activeCallContent.classList.contains('d-none'));

                stopCallTimer();

                // Only hide UI if we're still showing call UI
                const activeCallVisible = !activeCallContent.classList.contains('d-none');
                if (activeCallVisible) {
                    console.log('Hiding call UI from SIP callback');
                    hideCallUI();
                } else {
                    console.log('Call UI already hidden, skipping UI cleanup');
                }
            };

            sipManager.onCallFailed = function(error) {
                console.error('Call failed:', error);
                stopCallTimer();
                alert('Arama başlatılamadı: ' + error);
                endCall();
            };

            sipManager.onIncomingCall = function(invitation) {
                console.log('Incoming call received in UI');
                showIncomingCallUI(invitation);
            };

            sipManager.onIncomingCallTerminated = function(callerNumber) {
                console.log('Incoming call terminated by remote party before being answered:', callerNumber);
                console.log('Incoming call UI visible:', !incomingCallContent.classList.contains('d-none'));

                // Stop ringtone immediately when remote party hangs up
                stopRingtone();

                // Only hide incoming call UI if it's currently visible
                if (!incomingCallContent.classList.contains('d-none')) {
                    console.log('Hiding incoming call UI automatically');
                    hideIncomingCallUI();
                } else {
                    console.log('Incoming call UI already hidden, no action needed');
                }
            };

            sipManager.onRegistered = function() {
                console.log('SIP registered successfully');
                updateCallButtonState(true);
                updateSIPStatus('connected');
            };

            sipManager.onUnregistered = function() {
                console.log('SIP unregistered');
                updateCallButtonState(false);
                updateSIPStatus('disconnected');
            };

            sipManager.onError = function(type, message) {
                console.error('SIP Error:', type, message);
                updateSIPStatus('error', message);
                alert('SIP Hatası: ' + message);
            };

            // Initialize the SIP manager
            const success = await sipManager.initialize();
            if (success) {
                console.log('SIP Manager initialized successfully');
            } else {
                console.error('Failed to initialize SIP Manager');
                updateSIPStatus('error', 'Başlatılamadı');
            }
        } catch (error) {
            console.error('Error initializing SIP:', error);
            updateSIPStatus('error', 'Başlatılamadı');
            alert('SIP başlatılamadı: ' + error.message);
        } finally {
            isInitializingSIP = false;
        }
    }

    // Update call button state based on SIP readiness
    function updateCallButtonState(isReady) {
        if (callBtn) {
            if (isReady) {
                callBtn.style.opacity = '1';
                callBtn.style.cursor = 'pointer';
                callBtn.title = 'Arama yap';
            } else {
                callBtn.style.opacity = '0.5';
                callBtn.style.cursor = 'not-allowed';
                callBtn.title = 'SIP bağlantısı bekleniyor...';
            }
        }
    }

    // Show incoming call UI
    function showIncomingCallUI(invitation) {
        currentIncomingInvitation = invitation;

        // Extract caller information
        const callerNumber = invitation.remoteIdentity.uri.user;
        console.log('Incoming call from:', callerNumber);

        // Automatically open the dialpad if it's not already open
        if (typeof window.showDialpad === 'function') {
            console.log('Auto-opening dialpad for incoming call');
            window.showDialpad();
        }

        // Start ringtone for incoming call
        startRingtone();

        // Small delay to ensure dialpad is fully opened before showing incoming call UI
        setTimeout(() => {
            // Update incoming call UI
            if (incomingCallName && incomingCallNumber) {
                incomingCallName.textContent = 'Gelen Arama';
                incomingCallNumber.textContent = callerNumber;
            }

            // Hide all other content
            Object.values(contents).forEach(content => content.classList.add('d-none'));
            activeCallContent.classList.add('d-none');
            dialBottom.style.display = 'none';

            // Show incoming call UI
            incomingCallContent.classList.remove('d-none');
            incomingCallContent.classList.add('animate-fade-in');
        }, 100);
    }

    // Hide incoming call UI
    function hideIncomingCallUI() {
        currentIncomingInvitation = null;

        // Stop ringtone when hiding incoming call UI
        stopRingtone();

        // Hide incoming call UI
        incomingCallContent.classList.add('animate-fade-out');

        setTimeout(() => {
            incomingCallContent.classList.add('d-none');
            incomingCallContent.classList.remove('animate-fade-out');

            // Show dialpad content
            contents.dialpad.classList.remove('d-none');
            contents.dialpad.classList.add('animate-fade-in');
            dialBottom.style.display = 'grid';
        }, 150);
    }

    // Accept incoming call
    function acceptIncomingCall() {
        if (!currentIncomingInvitation) {
            console.error('No incoming invitation to accept');
            return;
        }

        console.log('Accepting incoming call');

        // Stop ringtone immediately when accepting call
        stopRingtone();

        // Get caller number for UI
        const callerNumber = currentIncomingInvitation.remoteIdentity.uri.user;

        // Accept the call through SIP manager
        if (sipManager) {
            sipManager.acceptIncomingCall(currentIncomingInvitation);
        }

        // Hide incoming call UI and show active call UI
        hideIncomingCallUI();

        // Show active call UI for incoming call
        setTimeout(() => {
            showCallUI(callerNumber, true);
        }, 200);
    }

    // Reject incoming call
    function rejectIncomingCall() {
        if (!currentIncomingInvitation) {
            console.error('No incoming invitation to reject');
            return;
        }

        console.log('Rejecting incoming call');

        // Stop ringtone immediately when rejecting call
        stopRingtone();

        // Stop any timer that might be running (safety measure)
        stopCallTimer();

        // Reject the call through SIP manager
        if (sipManager) {
            sipManager.rejectIncomingCall(currentIncomingInvitation);
        }

        // Hide incoming call UI
        hideIncomingCallUI();
    }

    // Update call UI with phone number and status
    function updateCallUI(phoneNumber, status) {
        const activeCallName = document.getElementById('activeCallName');
        const activeCallNumber = document.getElementById('activeCallNumber');

        if (activeCallName && activeCallNumber) {
            switch (status) {
                case 'connecting':
                    activeCallName.textContent = 'Aranıyor...';
                    break;
                case 'connected':
                    activeCallName.textContent = 'Konuşuyor';
                    break;
                default:
                    activeCallName.textContent = status === 'connecting' ? 'Aranıyor...' : 'Konuşuyor';
            }
            activeCallNumber.textContent = phoneNumber;
        }
    }

    // Update SIP status indicator
    function updateSIPStatus(status, message) {
        const sidebarSipStatus = document.getElementById('sidebarSipStatus');
        const sipStatus = document.getElementById('sipStatus');
        const statusText = sipStatus.querySelector('.status-text');

        if (sipStatus && statusText) {
            // Remove all status classes
            sipStatus.classList.remove('connected', 'disconnected', 'error');

            // Add appropriate class and update text
            switch (status) {
                case 'connecting':
                    statusText.textContent = 'Bağlanıyor...';
                    break;
                case 'connected':
                    sidebarSipStatus.classList.add('connected');
                    sipStatus.classList.add('connected');
                    statusText.textContent = 'Hazır';
                    break;
                case 'disconnected':
                    sidebarSipStatus.classList.remove('connected');
                    sipStatus.classList.add('disconnected');
                    statusText.textContent = 'Bağlantı Yok';
                    break;
                case 'error':
                    sidebarSipStatus.classList.remove('connected');
                    sipStatus.classList.add('error');
                    statusText.textContent = message || 'Hata';
                    break;
            }
        }
    }

    // Ringtone functions
    function initializeRingtone() {
        requestNotificationPermission();
        console.log('Initializing ringtone audio element');

        // Create audio element for ringtone
        ringtoneAudio = document.createElement('audio');
        ringtoneAudio.preload = 'auto';
        ringtoneAudio.loop = true;
        ringtoneAudio.volume = 0.3; // Set appropriate volume for notification

        // Set ringtone source - you can replace this with your preferred ringtone file
        // Try multiple formats for browser compatibility

        // window.authUser.ringtone kullanıcının rigtonunu boyle alabiliriz
        const ringtoneSources = [
            '/assets/sounds/ringtone.mp3',
            '/assets/sounds/ringtone.wav',
            '/assets/sounds/ringtone.ogg'
        ];

        // Try to find an available ringtone file
        let ringtoneSet = false;
        for (const src of ringtoneSources) {
            try {
                ringtoneAudio.src = src;
                ringtoneSet = true;
                console.log('Using ringtone source:', src);
                break;
            } catch (error) {
                console.warn('Failed to set ringtone source:', src, error);
            }
        }

        // Fallback to a simple beep tone if no ringtone file is available
        if (!ringtoneSet) {
            console.log('No ringtone file found, using fallback tone');
            // Simple tone generator - creates a basic ringtone sound
            ringtoneAudio.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
        }

        // Add to document body (hidden)
        ringtoneAudio.style.display = 'none';
        document.body.appendChild(ringtoneAudio);

        console.log('Ringtone audio element initialized');
    }
    
    function requestNotificationPermission() {
        if ('Notification' in window) {
            Notification.requestPermission().then(function (permission) {
                if (permission === "granted") {
                    // Bildirim gönderebilirsin
                    new Notification("Bildirim izni verildi!", { body: "Test bildirimi." });
                } else {
                    // Reddedildi veya kararsız
                    console.log("Bildirim izni verilmedi: ", permission);
                }
            });
        } else {
            console.log("Tarayıcı bildirimleri desteklemiyor.");
        }
    }

    function startRingtone() {
        if (!ringtoneAudio) {
            console.warn('Ringtone audio element not initialized');
            return;
        }

        console.log('Starting ringtone');

        try {
            // Reset audio to beginning
            ringtoneAudio.currentTime = 0;

            // Play the ringtone
            const playPromise = ringtoneAudio.play();

            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('Ringtone started successfully');
                }).catch(error => {
                    console.warn('Ringtone autoplay prevented by browser:', error.name, error.message);

                    // For incoming calls, we need to handle autoplay restrictions
                    // Show a visual notification that there's an incoming call
                    handleAutoplayRestriction();
                });
            }
        } catch (error) {
            console.error('Error starting ringtone:', error);
            handleAutoplayRestriction();
        }
    }

    function handleAutoplayRestriction() {
        console.log('Handling autoplay restriction - using visual/vibration alternatives');

        // Try to use vibration API if available (mobile devices)
        if ('vibrate' in navigator) {
            // Vibration pattern: vibrate for 500ms, pause 500ms, repeat
            const vibrationPattern = [500, 500, 500, 500, 500, 500];
            navigator.vibrate(vibrationPattern);
            console.log('Using vibration for incoming call notification');
        }

        // Could also flash the browser tab title or show a more prominent visual indicator
        // The incoming call UI is already visible, so this provides additional notification

        // Set up a click listener to enable audio for future calls
        const enableAudioOnClick = function() {
            if (ringtoneAudio) {
                ringtoneAudio.play().then(() => {
                    ringtoneAudio.pause();
                    ringtoneAudio.currentTime = 0;
                    console.log('Audio context enabled for future ringtones');
                }).catch(e => console.log('Audio enable failed:', e));
            }
            document.removeEventListener('click', enableAudioOnClick);
        };

        document.addEventListener('click', enableAudioOnClick);
    }

    function enableAudioOnUserInteraction() {
        console.log('Setting up audio enablement on user interaction');

        const enableAudio = function() {
            if (ringtoneAudio) {
                // Try to play and immediately pause to enable audio context
                ringtoneAudio.play().then(() => {
                    ringtoneAudio.pause();
                    ringtoneAudio.currentTime = 0;
                    console.log('Audio context enabled successfully');
                }).catch(e => {
                    console.log('Audio context enable attempt:', e.message);
                });
            }

            // Remove listeners after first successful interaction
            document.removeEventListener('click', enableAudio);
            document.removeEventListener('touchstart', enableAudio);
            document.removeEventListener('keydown', enableAudio);
        };

        // Listen for various user interaction events
        document.addEventListener('click', enableAudio);
        document.addEventListener('touchstart', enableAudio);
        document.addEventListener('keydown', enableAudio);
    }

    function stopRingtone() {
        if (!ringtoneAudio) {
            console.warn('Ringtone audio element not initialized');
            return;
        }

        console.log('Stopping ringtone');

        try {
            ringtoneAudio.pause();
            ringtoneAudio.currentTime = 0;
            console.log('Ringtone stopped successfully');
        } catch (error) {
            console.error('Error stopping ringtone:', error);
        }
    }

    // Call timer functions
    function startCallTimer() {
        console.log('startCallTimer() called');

        // Prevent duplicate timers
        if (callTimer) {
            console.warn('Call timer already running, stopping existing timer first');
            stopCallTimer();
        }

        console.log('Starting new call timer');
        callStartTime = Date.now();
        callDuration = 0;

        // Update timer immediately
        updateCallTimer();

        // Start interval to update every second
        callTimer = setInterval(updateCallTimer, 1000);
        console.log('Call timer started successfully');
    }

    function updateCallTimer() {
        if (callStartTime) {
            callDuration = Math.floor((Date.now() - callStartTime) / 1000);
        } else {
            callDuration++;
        }

        const minutes = Math.floor(callDuration / 60);
        const seconds = callDuration % 60;
        const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        // Update the timer display in active call UI
        const timerElement = document.getElementById('callTimer');
        if (timerElement) {
            timerElement.textContent = timeString;
        }

        console.log('Call duration updated:', timeString);
    }

    function stopCallTimer() {
        console.log('stopCallTimer() called');
        console.log('Timer was active:', !!callTimer);
        console.log('Call start time:', callStartTime);
        console.log('Call duration:', callDuration);

        if (callTimer) {
            clearInterval(callTimer);
            callTimer = null;
            console.log('Call timer cleared successfully');
        } else {
            console.log('No active timer to stop');
        }

        callStartTime = null;
        callDuration = 0;

        // Reset timer display
        const timerElement = document.getElementById('callTimer');
        if (timerElement) {
            timerElement.textContent = '00:00';
            console.log('Timer display reset to 00:00');
        } else {
            console.warn('Timer element not found');
        }
    }

    // Tab değiştirme fonksiyonu
    function switchTab(targetId) {
        // Aktif içeriği bul
        const activeContent = Object.entries(contents).find(([, content]) => 
            !content.classList.contains('d-none')
        );
        
        if (activeContent) {
            // Mevcut içeriği fade out yap
            activeContent[1].classList.add('animate-fade-out');
            
            setTimeout(() => {
                // Mevcut içeriği gizle
                Object.values(contents).forEach(content => content.classList.add('d-none'));
                
                // Hedef içeriği göster ve animate et
                contents[targetId].classList.remove('d-none');
                contents[targetId].classList.add('animate-fade-in');
                
                // Active class'ını güncelle
                bottomItems.forEach(item => {
                    if(item.getAttribute('data-target') === targetId) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                });
                
                // Animasyon class'larını temizle
                setTimeout(() => {
                    activeContent[1].classList.remove('animate-fade-out');
                    contents[targetId].classList.remove('animate-fade-in');
                }, 300);
            }, 300);
        } else {
            // İlk yükleme için direkt göster
            contents[targetId].classList.remove('d-none');
            contents[targetId].classList.add('animate-fade-in');
            
            // Active class'ını güncelle
            bottomItems.forEach(item => {
                if(item.getAttribute('data-target') === targetId) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
            
            setTimeout(() => {
                contents[targetId].classList.remove('animate-fade-in');
            }, 300);
        }
    }

    // Bottom item click olayları
    bottomItems.forEach(item => {
        item.addEventListener('click', function() {
            const target = this.getAttribute('data-target');
            switchTab(target);
        });
    });

    // Call again button functionality
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('call-again-btn')) {
            e.preventDefault();
            e.stopPropagation();

            // Find the phone number from the parent element
            let phoneNumber = '';
            const parentItem = e.target.closest('.call-info, .contact-item');

            if (parentItem) {
                // Try to find phone number in various ways
                const numberElement = parentItem.querySelector('.contact-number');
                if (numberElement) {
                    phoneNumber = numberElement.textContent.trim();
                } else {
                    // For call history, try to extract from call name or other elements
                    const callName = parentItem.querySelector('.call-name');
                    if (callName) {
                        // If call name contains a number, use it
                        const nameText = callName.textContent.trim();
                        const numberMatch = nameText.match(/[\d+\-\s()]+/);
                        if (numberMatch) {
                            phoneNumber = numberMatch[0];
                        }
                    }
                }
            }

            if (phoneNumber) {
                // Set the number in dialpad and make call
                dialedNumber.value = phoneNumber.replace(/[^\d+]/g, '');
                startCall();
            } else {
                alert('Telefon numarası bulunamadı.');
            }
        }
    });

    // Tuş takımı olayları
    dialpadButtons.forEach(button => {
        button.addEventListener('click', function() {
            const value = this.getAttribute('data-value');
            dialedNumber.value += value;
        });
    });

    clearNumber.addEventListener('click', function() {
        dialedNumber.value = dialedNumber.value.slice(0, -1);
    });

    closeDialpad.addEventListener('click', function() {
        dialpadPanel.classList.add('hide');
        setTimeout(() => {
            dialpadPanel.style.display = 'none';
            dialpadPanel.classList.remove('hide');
            document.getElementById('showDialpadBtn').style.display = 'flex';
        }, 200);
    });

    // Çağrı kontrol butonları için event listeners
    if (muteBtn) {
        muteBtn.addEventListener('click', function() {
            this.classList.toggle('active');
        });
    }

    if (speakerBtn) {
        speakerBtn.addEventListener('click', function() {
            this.classList.toggle('active');
        });
    }

    if (endCallBtn) {
        endCallBtn.addEventListener('click', function() {
            console.log('End call button clicked');

            // Immediate visual feedback
            this.style.transform = 'scale(0.8) rotate(-12deg)';

            // End call immediately without delay
            endCall();

            // Reset button style after a short delay (for visual feedback only)
            setTimeout(() => {
                this.style.transform = '';
            }, 200);
        });
    }

    // Accept call button event listener
    if (acceptCallBtn) {
        acceptCallBtn.addEventListener('click', function() {
            console.log('Accept call button clicked');

            // Visual feedback
            this.style.transform = 'scale(0.9)';

            acceptIncomingCall();

            // Reset button transform
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    }

    // Reject call button event listener
    if (rejectCallBtn) {
        rejectCallBtn.addEventListener('click', function() {
            console.log('Reject call button clicked');

            // Visual feedback
            this.style.transform = 'scale(0.9)';

            rejectIncomingCall();

            // Reset button transform
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    }

    // Arama başlatma fonksiyonu
    async function startCall() {
        if (dialedNumber.value.startsWith('5') && !dialedNumber.value.startsWith('90')) {
            dialedNumber.value = '90' + dialedNumber.value;
        } else if (dialedNumber.value.startsWith('0') && !dialedNumber.value.startsWith('90')) {
            dialedNumber.value = '90' + dialedNumber.value.slice(1);
        }

        const phoneNumber = dialedNumber.value.trim();

        if (!phoneNumber) {
            alert('Lütfen bir telefon numarası girin.');
            return;
        }
    
        if (!sipManager || !sipManager.isReady()) {
            alert('SIP bağlantısı hazır değil. Lütfen bekleyin...');
            return;
        }

        // Show call UI first
        showCallUI();

        // Make the SIP call
        const success = await sipManager.makeCall(phoneNumber);
        if (!success) {
            // If call failed, return to dialpad
            endCall();
        }
    }

    // Show call UI
    function showCallUI(phoneNumber = null, isIncoming = false) {
        console.log('Showing call UI for:', phoneNumber, 'incoming:', isIncoming);

        // For incoming calls, show immediately without animation delay
        if (isIncoming) {
            // Hide all content immediately
            Object.values(contents).forEach(content => content.classList.add('d-none'));
            dialBottom.style.display = 'none';

            // Show active call UI
            activeCallContent.classList.remove('d-none');
            activeCallContent.classList.add('animate-fade-in');

            // Update UI with caller info
            const number = phoneNumber || '(Numara yok)';
            updateCallUI(number, 'connected');

            // Note: Timer will be started by onCallEstablished callback when SIP session is established
            console.log('Incoming call UI shown, waiting for SIP session to be established for timer start');
            return;
        }

        // For outgoing calls, use animation
        // Önce mevcut içeriği fade out yap
        contents.dialpad.classList.add('animate-fade-out');

        // Bottom bar'ı slide out yap
        dialBottom.classList.add('animate-slide-out');

        setTimeout(() => {
            // Mevcut içeriği gizle
            Object.values(contents).forEach(content => content.classList.add('d-none'));
            dialBottom.style.display = 'none';

            // Arama ekranını göster ve animate et
            activeCallContent.classList.remove('d-none');
            activeCallContent.classList.add('animate-fade-in');

            // Aranan numarayı göster
            const number = phoneNumber || dialedNumber.value || '(Numara yok)';
            updateCallUI(number, 'connecting');

            // Animasyon class'larını temizle
            contents.dialpad.classList.remove('animate-fade-out');
            dialBottom.classList.remove('animate-slide-out');
        }, 300);
    }

    // Aramayı sonlandırma fonksiyonu
    async function endCall() {
        console.log('endCall() called');

        // Stop timer immediately for responsive UI
        stopCallTimer();

        // End the SIP call if active
        if (sipManager && sipManager.currentSession) {
            console.log('Terminating SIP session...');
            try {
                await sipManager.endCall();
                console.log('SIP session terminated successfully');
            } catch (error) {
                console.error('Error terminating SIP session:', error);
            }
        } else {
            console.log('No active SIP session to terminate');
        }

        // Hide call UI and return to dialpad immediately
        hideCallUI();
    }

    // Hide call UI and return to dialpad
    function hideCallUI() {
        console.log('hideCallUI() called');

        // Always stop the call timer when hiding UI
        stopCallTimer();

        // Aktif çağrı ekranını fade out yap
        activeCallContent.classList.add('animate-fade-out');

        // 300ms bekleyip içerikleri değiştir
        setTimeout(() => {
            // Aktif çağrı ekranını gizle
            activeCallContent.classList.add('d-none');
            activeCallContent.classList.remove('animate-fade-out');

            // Bottom bar'ı göster
            dialBottom.style.display = 'grid';
            dialBottom.classList.add('animate-slide-in');

            // Tuş takımını göster
            contents.dialpad.classList.remove('d-none');
            contents.dialpad.classList.add('animate-fade-in');

            // Numarayı temizle
            dialedNumber.value = '';

            // Bottom bar'ı aktif et ve tuş takımını seç
            bottomItems.forEach(item => {
                if(item.getAttribute('data-target') === 'dialpad') {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });

            // Animasyon class'larını temizle
            setTimeout(() => {
                contents.dialpad.classList.remove('animate-fade-in');
                dialBottom.classList.remove('animate-slide-in');
            }, 300);
        }, 300);
    }

    // Arama butonuna tıklandığında
    if (callBtn) {
        callBtn.addEventListener('click', function() {
            if (dialedNumber.value.trim()) {
                startCall();
            } else {
                alert('Lütfen bir telefon numarası girin.');
            }
        });
    }

    // Initialize SIP when dialpad is first opened
    function initializeSIPIfNeeded() {
        if (!sipManager && !isInitializingSIP) {
            initializeSIP();
        }
    }

    // Make function globally available
    window.initializeSIPIfNeeded = initializeSIPIfNeeded;

    // Make debug function globally available
    window.debugSIPAudio = function() {
        if (sipManager) {
            sipManager.checkAudioSetup();
        } else {
            console.log('SIP Manager not initialized');
        }
    };

    // Make call timer debug function globally available
    window.debugCallTimer = function() {
        console.log('=== Call Timer Debug Info ===');
        console.log('Call timer active:', !!callTimer);
        console.log('Call start time:', callStartTime);
        console.log('Call duration (seconds):', callDuration);
        console.log('Timer element:', document.getElementById('callTimer'));
        console.log('Current display:', document.getElementById('callTimer')?.textContent);
        console.log('=== End Timer Debug Info ===');
    };

    // Make force terminate function globally available for debugging
    window.forceEndCall = function() {
        console.log('Force ending call from debug function');
        if (sipManager) {
            sipManager.forceTerminate();
        }
        endCall();
    };

    // Make connection debug function globally available
    window.debugSIPConnection = function() {
        if (sipManager) {
            sipManager.debugConnectionStatus();
        } else {
            console.log('SIP Manager not initialized');
        }
    };

    // Make incoming call debug function globally available
    window.debugIncomingCall = function() {
        console.log('=== Incoming Call Debug Info ===');
        console.log('Current incoming invitation:', currentIncomingInvitation);
        console.log('Incoming call UI visible:', !incomingCallContent.classList.contains('d-none'));
        console.log('Active call UI visible:', !activeCallContent.classList.contains('d-none'));
        console.log('Accept button:', acceptCallBtn);
        console.log('Reject button:', rejectCallBtn);
        console.log('=== End Incoming Call Debug Info ===');
    };

    // Make force stop timer function globally available for debugging
    window.forceStopTimer = function() {
        console.log('Force stopping call timer from debug function');
        stopCallTimer();
    };

    // Make timer debug function globally available
    window.debugTimer = function() {
        console.log('=== Call Timer Debug Info ===');
        console.log('Timer active:', !!callTimer);
        console.log('Timer ID:', callTimer);
        console.log('Call start time:', callStartTime);
        console.log('Call duration (seconds):', callDuration);
        console.log('Timer element:', document.getElementById('callTimer'));
        console.log('Current display:', document.getElementById('callTimer')?.textContent);
        console.log('Active call UI visible:', !activeCallContent.classList.contains('d-none'));
        console.log('=== End Timer Debug Info ===');
    };

    // Make incoming call termination test function globally available
    window.testIncomingCallTermination = function() {
        console.log('Testing incoming call termination...');
        if (sipManager && sipManager.onIncomingCallTerminated) {
            sipManager.onIncomingCallTerminated('1234567890');
        } else {
            console.log('SIP manager or callback not available');
        }
    };

    // Make ringtone test functions globally available
    window.testRingtone = function() {
        console.log('Testing ringtone...');
        startRingtone();
    };

    window.stopTestRingtone = function() {
        console.log('Stopping test ringtone...');
        stopRingtone();
    };

    window.enableAudioContext = function() {
        console.log('Manually enabling audio context...');
        enableAudioOnUserInteraction();
        // Trigger the enabler immediately
        if (ringtoneAudio) {
            ringtoneAudio.play().then(() => {
                ringtoneAudio.pause();
                ringtoneAudio.currentTime = 0;
                console.log('Audio context enabled manually');
            }).catch(e => console.log('Manual audio enable failed:', e));
        }
    };

    window.debugRingtone = function() {
        console.log('=== Ringtone Debug Info ===');
        console.log('Ringtone audio element:', ringtoneAudio);
        console.log('Ringtone source:', ringtoneAudio?.src);
        console.log('Ringtone paused:', ringtoneAudio?.paused);
        console.log('Ringtone current time:', ringtoneAudio?.currentTime);
        console.log('Ringtone volume:', ringtoneAudio?.volume);
        console.log('Ringtone loop:', ringtoneAudio?.loop);
        console.log('Ringtone ready state:', ringtoneAudio?.readyState);
        console.log('Ringtone network state:', ringtoneAudio?.networkState);
        console.log('Ringtone error:', ringtoneAudio?.error);
        console.log('=== End Ringtone Debug Info ===');
    };

    // Make WebSocket call state test functions globally available
    window.testCallStateNotification = function(state = 'on_call') {
        console.log('Testing call state notification:', state);
        if (sipManager) {
            sipManager.testCallStateNotification(state);
        } else {
            console.log('SIP manager not available');
        }
    };

    window.checkWebSocketStatus = function() {
        if (sipManager) {
            sipManager.checkWebSocketStatus();
        } else {
            console.log('SIP manager not available');
        }
    };

    window.debugWebSocket = function() {
        console.log('=== WebSocket Debug Info ===');
        console.log('WebSocket object:', window.ws);
        console.log('WebSocket ready state:', window.ws?.readyState);
        console.log('WebSocket URL:', window.ws?.url);
        console.log('Authentication parameters:');
        console.log('  - Company ID:', window.currentCompanyId);
        console.log('  - User Name:', window.currentUserName);
        console.log('  - User ID:', window.currentUserId);
        console.log('=== End WebSocket Debug Info ===');
    };

    // Initialize SIP when the page loads
    initializeSIPIfNeeded();

    // Cleanup SIP when page unloads
    window.addEventListener('beforeunload', function() {
        if (sipManager) {
            sipManager.cleanup();
        }
    });
});
</script>
